package dataset

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/features"
)

type FeatureColumn interface {
	GetValue(physicalIndex int) (any, error)
	GetNumericalValue(physicalIndex int) (float64, error)
	GetSize() int
	GetType() features.FeatureType
	IsNumerical() bool
}

type IntColumn struct {
	data     []int64
	nullMask []bool
}

func (c *IntColumn) GetValue(index int) (any, error) {
	if index >= len(c.data) {
		return nil, fmt.Errorf("index out of bounds")
	}
	if c.nullMask[index] {
		return nil, fmt.Errorf("missing value")
	}
	return c.data[index], nil
}
func (c *IntColumn) GetNumericalValue(index int) (float64, error) {
	if index >= len(c.data) {
		return 0, fmt.Errorf("index out of bounds")
	}
	if c.nullMask[index] {
		return 0, fmt.<PERSON><PERSON><PERSON>("missing value")
	}
	return float64(c.data[index]), nil
}
func (c *IntColumn) GetSize() int                  { return len(c.data) }
func (c *IntColumn) GetType() features.FeatureType { return features.IntegerFeature }
func (c *IntColumn) IsNumerical() bool             { return true }

type FloatColumn struct {
	data     []float64
	nullMask []bool
}

func (c *FloatColumn) GetValue(index int) (any, error) {
	if index >= len(c.data) {
		return nil, fmt.Errorf("index out of bounds")
	}
	if c.nullMask[index] {
		return nil, fmt.Errorf("missing value")
	}
	return c.data[index], nil
}
func (c *FloatColumn) GetNumericalValue(index int) (float64, error) {
	if index >= len(c.data) {
		return 0, fmt.Errorf("index out of bounds")
	}
	if c.nullMask[index] {
		return 0, fmt.Errorf("missing value")
	}
	return c.data[index], nil
}
func (c *FloatColumn) GetSize() int                  { return len(c.data) }
func (c *FloatColumn) GetType() features.FeatureType { return features.FloatFeature }
func (c *FloatColumn) IsNumerical() bool             { return true }

type StringColumn struct {
	data     []string
	nullMask []bool
}

func (c *StringColumn) GetValue(index int) (any, error) {
	if index >= len(c.data) {
		return nil, fmt.Errorf("index out of bounds")
	}
	if c.nullMask[index] {
		return nil, fmt.Errorf("missing value")
	}
	return c.data[index], nil
}
func (c *StringColumn) GetNumericalValue(index int) (float64, error) {
	return 0, fmt.Errorf("string column cannot be converted to numerical value")
}
func (c *StringColumn) GetSize() int                  { return len(c.data) }
func (c *StringColumn) GetType() features.FeatureType { return features.StringFeature }
func (c *StringColumn) IsNumerical() bool             { return false }
