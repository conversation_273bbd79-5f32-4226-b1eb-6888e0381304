// Package features provides YAML feature metadata loading capabilities.
//
// Loads only name and type values for each feature as specified in project
// requirements. Handles parsing and validation of YAML feature information
// files according to <PERSON><PERSON>berri specifications.
//
// Security: Validates file paths and content structure
// Performance: Efficient YAML parsing with early validation failures
package features

import (
	"fmt"
	"os"
	"strings"

	"github.com/berrijam/mulberri/internal/config"
	"gopkg.in/yaml.v3"
)

// YAMLFeatureInfo represents feature metadata from YAML configuration.
//
// Args: YAML unmarshaling (struct tags define mapping)
// Constraints: Both Type and HandleAs are required fields
// Security: No sensitive data storage
// Relationships: Maps to project-specified YAML format
// Side effects: Used for unmarshaling YAML content into Go structs
//
// Example YAML:
//
//	weather:
//	  type: nominal
//	  handle_as: string
type YAMLFeatureInfo struct {
	Type     string `yaml:"type"`      // Feature type from project spec (nominal/numeric/etc.)
	HandleAs string `yaml:"handle_as"` // Core type conversion target (integer/float/string)
}

// YAMLFeatureConfig maps feature names to their YAML definitions.
//
// Args: Map operations for feature metadata access
// Security: Contains metadata only, no sensitive training data
// Performance: O(1) feature lookups by name
// Relationships: Output of YAML parsing, input to data loading process
type YAMLFeatureConfig map[string]YAMLFeatureInfo

// FeatureLoader handles parsing and validation of YAML feature information files.
//
// Args: File path operations for YAML processing
// Security: Validates file paths and content structure
// Performance: Stateless design enables concurrent usage
// Relationships: Used by data loading pipeline to get feature specifications
// Side effects: Reads files from disk, validates content structure
type FeatureLoader struct{}

// FeatureLoaderError represents errors during feature loading operations.
//
// Args: Structured error context for debugging
// Security: May contain file paths (ensure no sensitive path exposure)
// Relationships: Used throughout loading pipeline for error reporting
// Side effects: Provides context for error handling and user feedback
type FeatureLoaderError struct {
	Op      string // Operation that failed (parse, validate, etc.)
	File    string // File path being processed
	Feature string // Specific feature name (if applicable)
	Err     error  // Underlying error
}

// Error implements the error interface with structured context.
//
// Args: None (method receiver)
// Returns: formatted error message with operation context
// Performance: String formatting operation
func (e *FeatureLoaderError) Error() string {
	if e.Feature != "" {
		return fmt.Sprintf("feature loader %s error for feature '%s' in file '%s': %v",
			e.Op, e.Feature, e.File, e.Err)
	}
	return fmt.Sprintf("feature loader %s error in file '%s': %v", e.Op, e.File, e.Err)
}

// Unwrap returns the underlying error for error chain inspection.
//
// Args: None (method receiver)
// Returns: underlying error for error wrapping support
// Relationships: Supports Go 1.13+ error unwrapping
func (e *FeatureLoaderError) Unwrap() error {
	return e.Err
}

// NewFeatureLoader creates a new YAML feature loader instance.
//
// Args: None
// Returns: loader ready for parsing YAML files
// Security: Stateless design prevents state corruption
// Performance: No initialization overhead
func NewFeatureLoader() *FeatureLoader {
	return &FeatureLoader{}
}

// LoadFeatureInfo parses a YAML feature information file and loads name/type values.
//
// Args:
// - filePath: Path to YAML feature info file (must exist and be readable)
//
// Returns:
// - *YAMLFeatureConfig: parsed configuration mapping features to metadata
// - error: parsing or validation error
//
// Security: Validates file path and content structure
// Performance: Efficient YAML parsing with early error detection
// Relationships: Primary interface for loading feature specifications
// Side effects: Reads file from disk, validates against project spec
//
// Example YAML format from project spec:
//
//	weather:
//	  type: nominal
//	  handle_as: string
//	temperature:
//	  type: numeric
//	  handle_as: float
//	age:
//	  type: numeric
//	  handle_as: integer
//	played_on:
//	  type: datetime
//	  handle_as: integer
//	play_tennis:
//	  type: binary
//	  handle_as: string
func (fl *FeatureLoader) LoadFeatureInfo(filePath string) (*YAMLFeatureConfig, error) {
	// Read and validate file content
	data, err := fl.readAndValidateFile(filePath)
	if err != nil {
		return nil, err // Already wrapped with context
	}

	// Parse YAML content
	var config YAMLFeatureConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, &FeatureLoaderError{
			Op:   "parse_yaml",
			File: filePath,
			Err:  fmt.Errorf("invalid YAML format: %w", err),
		}
	}

	// Validate parsed configuration against project specifications
	if err := fl.validateConfiguration(config, filePath); err != nil {
		return nil, err // Already wrapped in validateConfiguration
	}

	return &config, nil
}

// readAndValidateFile reads file content and performs basic validation.
//
// Args:
// - filePath: Path to file to read
//
// Returns:
// - []byte: file content
// - error: file access or validation error
//
// Security: Validates file accessibility and non-empty content
// Performance: Single file read operation
// Side effects: Reads file from disk
func (fl *FeatureLoader) readAndValidateFile(filePath string) ([]byte, error) {
	// Read file contents
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, &FeatureLoaderError{
			Op:   "read_file",
			File: filePath,
			Err:  fmt.Errorf("cannot read file: %w", err),
		}
	}

	// Check for empty file
	if len(strings.TrimSpace(string(data))) == 0 {
		return nil, &FeatureLoaderError{
			Op:   "validate_content",
			File: filePath,
			Err:  fmt.Errorf("file is empty or contains only whitespace"),
		}
	}

	return data, nil
}

// validateConfiguration validates overall configuration structure and individual features.
//
// Args:
// - config: Parsed YAML configuration to validate
// - filePath: File path for error context
//
// Returns: error if configuration is invalid, nil if valid
// Security: Validates against project-specified constraints
// Performance: Iterates through all features for validation
// Side effects: Validates each feature in configuration
func (fl *FeatureLoader) validateConfiguration(config YAMLFeatureConfig, filePath string) error {
	if len(config) == 0 {
		return &FeatureLoaderError{
			Op:   "validate_config",
			File: filePath,
			Err:  fmt.Errorf("no features defined in configuration"),
		}
	}

	// Validate each feature against project specifications
	for featureName, featureInfo := range config {
		if err := fl.validateFeature(featureName, featureInfo, filePath); err != nil {
			return err // Already wrapped with context
		}
	}

	return nil
}

// validateFeature validates a single feature's YAML configuration.
//
// Args:
// - featureName: Name of feature being validated (must be non-empty)
// - featureInfo: YAML feature configuration to validate
// - filePath: File path for error context
//
// Returns: error if feature is invalid, nil if valid
// Security: Validates against supported types from config package
// Performance: Multiple validation checks per feature
// Relationships: Uses config.SupportedFeatureTypes and config.SupportedHandleAsTypes
// Side effects: Comprehensive validation of feature specification
func (fl *FeatureLoader) validateFeature(featureName string, featureInfo YAMLFeatureInfo, filePath string) error {
	// Validate feature name
	if strings.TrimSpace(featureName) == "" {
		return &FeatureLoaderError{
			Op:      "validate_feature",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("feature name cannot be empty or whitespace-only"),
		}
	}

	// Validate type field against project specifications
	if err := fl.validateFeatureType(featureInfo.Type); err != nil {
		return &FeatureLoaderError{
			Op:      "validate_feature",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("invalid type: %w", err),
		}
	}

	// Validate handle_as field against project specifications
	if err := fl.validateHandleAs(featureInfo.HandleAs); err != nil {
		return &FeatureLoaderError{
			Op:      "validate_feature",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("invalid handle_as: %w", err),
		}
	}

	// Validate type and handle_as compatibility
	if err := fl.validateTypeCompatibility(featureInfo.Type, featureInfo.HandleAs); err != nil {
		return &FeatureLoaderError{
			Op:      "validate_feature",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("type/handle_as incompatibility: %w", err),
		}
	}

	return nil
}

// validateFeatureType validates that feature type is supported by project spec.
//
// Args:
// - featureType: Type string to validate
//
// Returns: error if type unsupported, nil if supported
// Constraints: Must be in config.SupportedFeatureTypes list
// Relationships: References supported types from config package
// Performance: Linear search through supported types list
func (fl *FeatureLoader) validateFeatureType(featureType string) error {
	normalizedType := strings.ToLower(strings.TrimSpace(featureType))

	if normalizedType == "" {
		return fmt.Errorf("type field is required")
	}

	// Check against supported types from project specification
	for _, supportedType := range config.SupportedFeatureTypes {
		if normalizedType == supportedType {
			return nil
		}
	}

	return fmt.Errorf("unsupported type '%s', must be one of: %v",
		featureType, config.SupportedFeatureTypes)
}

// validateHandleAs validates that handle_as value is supported by project spec.
//
// Args:
// - handleAs: Handle_as string to validate
//
// Returns: error if handle_as unsupported, nil if supported
// Constraints: Must be in config.SupportedHandleAsTypes list
// Relationships: References supported handle_as types from config package
// Performance: Linear search through supported handle_as list
func (fl *FeatureLoader) validateHandleAs(handleAs string) error {
	normalizedHandleAs := strings.ToLower(strings.TrimSpace(handleAs))

	if normalizedHandleAs == "" {
		return fmt.Errorf("handle_as field is required")
	}

	// Check against supported handle_as types from project specification
	for _, supportedHandleAs := range config.SupportedHandleAsTypes {
		if normalizedHandleAs == supportedHandleAs {
			return nil
		}
	}

	return fmt.Errorf("unsupported handle_as '%s', must be one of: %v",
		handleAs, config.SupportedHandleAsTypes)
}

// validateTypeCompatibility validates logical compatibility between type and handle_as.
//
// Args:
// - featureType: Feature type string (normalized)
// - handleAs: Handle_as string (normalized)
//
// Returns: error for incompatible combinations, nil if compatible
// Constraints: Prevents illogical combinations (e.g., nominal->float)
// Performance: Map lookup for incompatibility rules
// Side effects: Helps prevent data processing errors downstream
func (fl *FeatureLoader) validateTypeCompatibility(featureType, handleAs string) error {
	normalizedType := strings.ToLower(strings.TrimSpace(featureType))
	normalizedHandleAs := strings.ToLower(strings.TrimSpace(handleAs))

	// Define clearly incompatible combinations based on logical constraints
	incompatibleCombinations := map[string][]string{
		"nominal": {"float"}, // Nominal features shouldn't be float
		"binary":  {"float"}, // Binary features shouldn't be float
	}

	if incompatibleTypes, exists := incompatibleCombinations[normalizedType]; exists {
		for _, incompatibleType := range incompatibleTypes {
			if normalizedHandleAs == incompatibleType {
				return fmt.Errorf("type '%s' is not compatible with handle_as '%s'",
					featureType, handleAs)
			}
		}
	}

	// All other combinations allowed for flexibility
	return nil
}

// ValidateFeatureInfoAgainstCSV validates that feature info matches CSV headers.
//
// Args:
// - config: Parsed feature info configuration
// - csvHeaders: Column headers from CSV file
// - filePath: File path for error context
//
// Returns: error if validation fails, nil if valid
// Security: Validates data structure consistency
// Performance: O(n) iteration through features and headers
// Relationships: Used during data loading to ensure compatibility
// Side effects: Validates that YAML features exist in CSV
//
// Checks that all features defined in YAML exist as columns in CSV.
// Does not require all CSV columns to be defined in feature info (partial definitions allowed).
func ValidateFeatureInfoAgainstCSV(config *YAMLFeatureConfig, csvHeaders []string, filePath string) error {
	return ValidateFeatureInfoAgainstCSVWithTarget(config, csvHeaders, "", filePath)
}

// ValidateFeatureInfoAgainstCSVWithTarget validates feature info against CSV, excluding target column.
//
// Args:
// - config: Parsed feature info configuration
// - csvHeaders: Column headers from CSV file
// - targetColumn: Target column name to exclude from validation
// - filePath: File path for error context
//
// Returns: error if validation fails, nil if valid
// Security: Validates data structure consistency
// Performance: O(n) iteration with hash map lookups
// Relationships: Used during training to allow target column metadata
// Side effects: Validates feature-to-column mapping
//
// Allows target column to be defined in feature info without requiring it to be a feature.
// Useful for training scenarios where target column has metadata but isn't a feature.
func ValidateFeatureInfoAgainstCSVWithTarget(config *YAMLFeatureConfig, csvHeaders []string, targetColumn string, filePath string) error {
	if config == nil {
		return &FeatureLoaderError{
			Op:   "validate_csv",
			File: filePath,
			Err:  fmt.Errorf("feature config is nil"),
		}
	}

	// Create map of CSV headers for efficient lookup
	csvHeaderMap := make(map[string]bool)
	for _, header := range csvHeaders {
		csvHeaderMap[header] = true
	}

	// Check each feature defined in YAML exists in CSV
	var missingInCSV []string
	for featureName := range *config {
		if !csvHeaderMap[featureName] {
			missingInCSV = append(missingInCSV, featureName)
		}
	}

	if len(missingInCSV) > 0 {
		return &FeatureLoaderError{
			Op:   "validate_csv",
			File: filePath,
			Err:  fmt.Errorf("features defined in YAML but missing in CSV: %v", missingInCSV),
		}
	}

	return nil
}
