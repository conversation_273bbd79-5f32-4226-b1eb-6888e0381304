// Package features provides feature type information and metadata management.
//
// Saves only the name, type, and distribution from training data as specified
// in project requirements. Handles runtime feature metadata storage and
// distribution tracking during decision tree training process.
//
// Security: Thread-safe for concurrent access during training and prediction
// Performance: Efficient metadata lookups and distribution operations
package features

// FeatureType represents the core feature types supported by Mulberri.
//
// Args: None (enum type)
// Constraints: Must be one of IntegerFeature, FloatFeature, StringFeature
// Security: No sensitive data handling
// Relationships: Maps to handle_as values from YAML configuration
// Side effects: Determines internal data processing and storage types
type FeatureType int

const (
	IntegerFeature FeatureType = iota // Converted to int64 internally
	FloatFeature                      // Converted to float64 internally
	StringFeature                     // Stored as string internally
)

// String returns the string representation of FeatureType.
//
// Args: None (method receiver)
// Returns: string representation of feature type
// Security: No sensitive data exposure
func (ft FeatureType) String() string {
	switch ft {
	case IntegerFeature:
		return "integer"
	case FloatFeature:
		return "float"
	case StringFeature:
		return "string"
	default:
		return "unknown"
	}
}

// FeatureInfo holds metadata about a single feature from training data.
//
// Args: Created during data loading process
// Security: Contains training data distribution (no PII directly)
// Performance: O(1) lookups for metadata, O(n) for distribution operations
// Relationships: Created during data loading, used throughout training
// Side effects: Distribution updates affect memory usage linearly
type FeatureInfo struct {
	Name         string              // Feature name (CSV column header)
	Type         FeatureType         // Internal processing type (integer/float/string)
	OriginalType string              // Original YAML type specification (nominal/numeric/etc.)
	Distribution map[interface{}]int // Value distribution from training data
}

// NewFeatureInfo creates a new FeatureInfo with the specified parameters.
//
// Args:
// - name: Feature name (must be non-empty CSV column header)
// - featureType: Internal processing type (IntegerFeature/FloatFeature/StringFeature)
// - originalType: Original YAML type specification (nominal/numeric/datetime/etc.)
//
// Returns: initialized FeatureInfo with empty distribution
// Security: No validation of name content (assumes pre-validated)
// Relationships: Used by data loading process to create feature metadata
// Side effects: Allocates memory for distribution map
func NewFeatureInfo(name string, featureType FeatureType, originalType string) *FeatureInfo {
	return &FeatureInfo{
		Name:         name,
		Type:         featureType,
		OriginalType: originalType,
		Distribution: make(map[interface{}]int),
	}
}

// IsNumerical returns true if the feature type is numeric (int or float).
//
// Args: None (method receiver)
// Returns: true for IntegerFeature or FloatFeature, false otherwise
// Relationships: Used by splitting algorithms to determine strategy (threshold vs value-based)
// Performance: O(1) type check operation
func (fi *FeatureInfo) IsNumerical() bool {
	return fi.Type == IntegerFeature || fi.Type == FloatFeature
}

// IsCategorical returns true if the feature type is categorical (string).
//
// Args: None (method receiver)
// Returns: true for StringFeature, false otherwise
// Relationships: Used for categorical splitting and missing value handling
// Performance: O(1) type check operation
func (fi *FeatureInfo) IsCategorical() bool {
	return fi.Type == StringFeature
}

// AddValue adds a value to the feature's distribution during training data loading.
//
// Args:
// - value: Feature value to add (any comparable type: int64, float64, string)
//
// Side effects: Updates distribution map, increases memory usage
// Performance: O(1) hash map operation
// Relationships: Called during Dataset loading for each training sample
func (fi *FeatureInfo) AddValue(value interface{}) {
	fi.Distribution[value]++
}

// GetUniqueValueCount returns the number of unique values seen for this feature.
//
// Args: None (method receiver)
// Returns: count of unique values in distribution
// Performance: O(1) operation using map length
// Relationships: Used for feature cardinality analysis and splitting decisions
func (fi *FeatureInfo) GetUniqueValueCount() int {
	return len(fi.Distribution)
}

// GetMostCommonValue returns the most frequently occurring value and its count.
//
// Args: None (method receiver)
// Returns:
// - interface{}: most common value (nil if no values recorded)
// - int: count of occurrences (0 if no values recorded)
//
// Performance: O(n) iteration through distribution map
// Relationships: Used for missing value imputation strategies
// Side effects: None (read-only operation on distribution)
func (fi *FeatureInfo) GetMostCommonValue() (interface{}, int) {
	var mostCommon interface{}
	maxCount := 0

	for value, count := range fi.Distribution {
		if count > maxCount {
			maxCount = count
			mostCommon = value
		}
	}

	return mostCommon, maxCount
}

// GetDistribution returns a copy of the feature's value distribution.
//
// Args: None (method receiver)
// Returns: copy of distribution map (safe for external modification)
// Security: Returns copy to prevent external modification of internal state
// Performance: O(n) copy operation
func (fi *FeatureInfo) GetDistribution() map[interface{}]int {
	dist := make(map[interface{}]int)
	for value, count := range fi.Distribution {
		dist[value] = count
	}
	return dist
}

// GetTotalSamples returns the total number of samples seen for this feature.
//
// Args: None (method receiver)
// Returns: total count of all values in distribution
// Performance: O(n) summation over distribution values
// Relationships: Used for statistical calculations and validation
func (fi *FeatureInfo) GetTotalSamples() int {
	total := 0
	for _, count := range fi.Distribution {
		total += count
	}
	return total
}

// FeatureInfoMap holds metadata for all features in a dataset.
//
// Args: Map operations (get, set, iterate)
// Security: Immutable after training data loading for consistency
// Performance: O(1) feature metadata lookups by name
// Relationships: Used throughout training and prediction workflows
// Side effects: Stores feature metadata in memory
type FeatureInfoMap map[string]*FeatureInfo

// NewFeatureInfoMap creates an empty feature info map.
//
// Args: None
// Returns: initialized empty map ready for population during data loading
// Side effects: Allocates memory for map structure
// Relationships: Used at start of training data loading process
func NewFeatureInfoMap() FeatureInfoMap {
	return make(FeatureInfoMap)
}

// AddFeature adds a feature to the map.
//
// Args:
// - feature: FeatureInfo pointer to add (must be non-nil)
//
// Side effects: Stores feature in map, may overwrite existing feature with same name
// Security: No validation of feature content (assumes pre-validated)
// Performance: O(1) hash map insertion
func (fim FeatureInfoMap) AddFeature(feature *FeatureInfo) {
	fim[feature.Name] = feature
}

// GetFeature retrieves feature metadata by name.
//
// Args:
// - name: Feature name to lookup (CSV column header)
//
// Returns:
// - *FeatureInfo: feature metadata (nil if not found)
// - bool: true if found, false otherwise
//
// Performance: O(1) hash map lookup
// Relationships: Used throughout training for feature metadata access
func (fim FeatureInfoMap) GetFeature(name string) (*FeatureInfo, bool) {
	feature, exists := fim[name]
	return feature, exists
}

// GetFeatureNames returns all feature names in the map.
//
// Args: None (method receiver)
// Returns: slice of feature names (order not guaranteed)
// Performance: O(n) where n is number of features
// Relationships: Used for iteration over all features during processing
func (fim FeatureInfoMap) GetFeatureNames() []string {
	names := make([]string, 0, len(fim))
	for name := range fim {
		names = append(names, name)
	}
	return names
}

// GetFeatureCount returns the total number of features in the map.
//
// Args: None (method receiver)
// Returns: count of features stored in map
// Performance: O(1) operation using map length
func (fim FeatureInfoMap) GetFeatureCount() int {
	return len(fim)
}

// HasFeature checks if a feature exists in the map.
//
// Args:
// - name: Feature name to check
//
// Returns: true if feature exists, false otherwise
// Performance: O(1) hash map lookup
// Relationships: Used for validation during data processing
func (fim FeatureInfoMap) HasFeature(name string) bool {
	_, exists := fim[name]
	return exists
}
