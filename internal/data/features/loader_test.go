package features

import (
	"errors"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// TestValidateTypeCompatibility tests the type compatibility validation function.
func TestValidateTypeCompatibility(t *testing.T) {
	loader := NewFeatureLoader()

	tests := []struct {
		name        string
		featureType string
		handleAs    string
		wantErr     bool
		errMsg      string
	}{
		// Valid combinations
		{
			name:        "nominal with string - valid",
			featureType: "nominal",
			handleAs:    "string",
			wantErr:     false,
		},
		{
			name:        "numeric with float - valid",
			featureType: "numeric",
			handleAs:    "float",
			wantErr:     false,
		},
		{
			name:        "numeric with integer - valid",
			featureType: "numeric",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "date with integer - valid",
			featureType: "date",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "datetime with integer - valid",
			featureType: "datetime",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "time with integer - valid",
			featureType: "time",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "binary with string - valid",
			featureType: "binary",
			handleAs:    "string",
			wantErr:     false,
		},
		{
			name:        "binary with integer - valid",
			featureType: "binary",
			handleAs:    "integer",
			wantErr:     false,
		},
		// Case insensitive tests
		{
			name:        "NOMINAL with STRING - case insensitive",
			featureType: "NOMINAL",
			handleAs:    "STRING",
			wantErr:     false,
		},
		{
			name:        "Numeric with Float - mixed case",
			featureType: "Numeric",
			handleAs:    "Float",
			wantErr:     false,
		},
		// Invalid combinations (only these are actually incompatible)
		{
			name:        "nominal with float - invalid",
			featureType: "nominal",
			handleAs:    "float",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		{
			name:        "binary with float - invalid",
			featureType: "binary",
			handleAs:    "float",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		// Valid combinations that might seem invalid but are actually allowed
		{
			name:        "nominal with integer - valid (allowed for flexibility)",
			featureType: "nominal",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "numeric with string - valid (allowed for flexibility)",
			featureType: "numeric",
			handleAs:    "string",
			wantErr:     false,
		},
		{
			name:        "date with string - valid (allowed for flexibility)",
			featureType: "date",
			handleAs:    "string",
			wantErr:     false,
		},
		{
			name:        "date with float - valid (allowed for flexibility)",
			featureType: "date",
			handleAs:    "float",
			wantErr:     false,
		},
		// Whitespace handling
		{
			name:        "whitespace handling - valid",
			featureType: "  nominal  ",
			handleAs:    "  string  ",
			wantErr:     false,
		},
		{
			name:        "whitespace handling - invalid",
			featureType: "  nominal  ",
			handleAs:    "  float  ",
			wantErr:     true,
			errMsg:      "not compatible",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := loader.validateTypeCompatibility(tt.featureType, tt.handleAs)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// BenchmarkValidateTypeCompatibility benchmarks the type compatibility validation.
func BenchmarkValidateTypeCompatibility(b *testing.B) {
	loader := NewFeatureLoader()

	// Test with a mix of valid and invalid combinations
	testCases := []struct {
		featureType string
		handleAs    string
	}{
		{"nominal", "string"},
		{"numeric", "float"},
		{"binary", "integer"},
		{"nominal", "float"}, // invalid
		{"date", "integer"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		testCase := testCases[i%len(testCases)]
		_ = loader.validateTypeCompatibility(testCase.featureType, testCase.handleAs)
	}
}

// TestFeatureLoaderError tests the FeatureLoaderError type and its methods.
func TestFeatureLoaderError(t *testing.T) {
	tests := []struct {
		name     string
		err      *FeatureLoaderError
		expected string
	}{
		{
			name: "error with feature",
			err: &FeatureLoaderError{
				Op:      "validate_feature",
				File:    "test.yaml",
				Feature: "age",
				Err:     errors.New("invalid type"),
			},
			expected: "feature loader validate_feature error for feature 'age' in file 'test.yaml': invalid type",
		},
		{
			name: "error without feature",
			err: &FeatureLoaderError{
				Op:   "parse_yaml",
				File: "test.yaml",
				Err:  errors.New("syntax error"),
			},
			expected: "feature loader parse_yaml error in file 'test.yaml': syntax error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.err.Error(); got != tt.expected {
				t.Errorf("FeatureLoaderError.Error() = %q, want %q", got, tt.expected)
			}
		})
	}
}

// TestFeatureLoaderErrorUnwrap tests the Unwrap method.
func TestFeatureLoaderErrorUnwrap(t *testing.T) {
	originalErr := errors.New("original error")
	loaderErr := &FeatureLoaderError{
		Op:   "test",
		File: "test.yaml",
		Err:  originalErr,
	}

	if unwrapped := loaderErr.Unwrap(); unwrapped != originalErr {
		t.Errorf("FeatureLoaderError.Unwrap() = %v, want %v", unwrapped, originalErr)
	}
}

// TestNewFeatureLoader tests the constructor.
func TestNewFeatureLoader(t *testing.T) {
	loader := NewFeatureLoader()
	if loader == nil {
		t.Error("NewFeatureLoader() returned nil")
	}
}

// TestValidateFeatureType tests the validateFeatureType method.
func TestValidateFeatureType(t *testing.T) {
	loader := NewFeatureLoader()

	tests := []struct {
		name        string
		featureType string
		wantErr     bool
		errContains string
	}{
		// Valid types
		{
			name:        "nominal type",
			featureType: "nominal",
			wantErr:     false,
		},
		{
			name:        "numeric type",
			featureType: "numeric",
			wantErr:     false,
		},
		{
			name:        "date type",
			featureType: "date",
			wantErr:     false,
		},
		{
			name:        "datetime type",
			featureType: "datetime",
			wantErr:     false,
		},
		{
			name:        "binary type",
			featureType: "binary",
			wantErr:     false,
		},
		{
			name:        "time type",
			featureType: "time",
			wantErr:     false,
		},
		// Case insensitive
		{
			name:        "NOMINAL type - case insensitive",
			featureType: "NOMINAL",
			wantErr:     false,
		},
		{
			name:        "Numeric type - mixed case",
			featureType: "Numeric",
			wantErr:     false,
		},
		// With whitespace
		{
			name:        "nominal with whitespace",
			featureType: "  nominal  ",
			wantErr:     false,
		},
		// Invalid types
		{
			name:        "empty type",
			featureType: "",
			wantErr:     true,
			errContains: "type field is required",
		},
		{
			name:        "whitespace only type",
			featureType: "   ",
			wantErr:     true,
			errContains: "type field is required",
		},
		{
			name:        "unsupported type",
			featureType: "unsupported",
			wantErr:     true,
			errContains: "unsupported type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := loader.validateFeatureType(tt.featureType)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// TestValidateHandleAs tests the validateHandleAs method.
func TestValidateHandleAs(t *testing.T) {
	loader := NewFeatureLoader()

	tests := []struct {
		name        string
		handleAs    string
		wantErr     bool
		errContains string
	}{
		// Valid handle_as values
		{
			name:     "integer handle_as",
			handleAs: "integer",
			wantErr:  false,
		},
		{
			name:     "float handle_as",
			handleAs: "float",
			wantErr:  false,
		},
		{
			name:     "string handle_as",
			handleAs: "string",
			wantErr:  false,
		},
		// Case insensitive
		{
			name:     "INTEGER handle_as - case insensitive",
			handleAs: "INTEGER",
			wantErr:  false,
		},
		{
			name:     "Float handle_as - mixed case",
			handleAs: "Float",
			wantErr:  false,
		},
		// With whitespace
		{
			name:     "string with whitespace",
			handleAs: "  string  ",
			wantErr:  false,
		},
		// Invalid handle_as values
		{
			name:        "empty handle_as",
			handleAs:    "",
			wantErr:     true,
			errContains: "handle_as field is required",
		},
		{
			name:        "whitespace only handle_as",
			handleAs:    "   ",
			wantErr:     true,
			errContains: "handle_as field is required",
		},
		{
			name:        "unsupported handle_as",
			handleAs:    "unsupported",
			wantErr:     true,
			errContains: "unsupported handle_as",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := loader.validateHandleAs(tt.handleAs)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// TestLoadFeatureInfo tests the LoadFeatureInfo method with file operations.
func TestLoadFeatureInfo(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := os.MkdirTemp("", "loader_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	loader := NewFeatureLoader()

	tests := []struct {
		name        string
		content     string
		wantErr     bool
		errContains string
		validate    func(t *testing.T, config *YAMLFeatureConfig)
	}{
		{
			name: "valid YAML configuration",
			content: `weather:
  type: nominal
  handle_as: string
temperature:
  type: numeric
  handle_as: float
age:
  type: numeric
  handle_as: integer`,
			wantErr: false,
			validate: func(t *testing.T, config *YAMLFeatureConfig) {
				if len(*config) != 3 {
					t.Errorf("Expected 3 features, got %d", len(*config))
				}

				if weather, exists := (*config)["weather"]; exists {
					if weather.Type != "nominal" || weather.HandleAs != "string" {
						t.Errorf("Weather feature incorrect: %+v", weather)
					}
				} else {
					t.Error("Weather feature not found")
				}

				if temp, exists := (*config)["temperature"]; exists {
					if temp.Type != "numeric" || temp.HandleAs != "float" {
						t.Errorf("Temperature feature incorrect: %+v", temp)
					}
				} else {
					t.Error("Temperature feature not found")
				}
			},
		},
		{
			name:        "empty file",
			content:     "",
			wantErr:     true,
			errContains: "empty or contains only whitespace",
		},
		{
			name:        "whitespace only file",
			content:     "   \n\t  \n  ",
			wantErr:     true,
			errContains: "empty or contains only whitespace",
		},
		{
			name:        "invalid YAML syntax",
			content:     "weather:\n  type: nominal\n  handle_as: [unclosed",
			wantErr:     true,
			errContains: "invalid YAML format",
		},
		{
			name:        "empty configuration",
			content:     "{}",
			wantErr:     true,
			errContains: "no features defined in configuration",
		},
		{
			name: "invalid feature type",
			content: `weather:
  type: invalid_type
  handle_as: string`,
			wantErr:     true,
			errContains: "unsupported type",
		},
		{
			name: "invalid handle_as",
			content: `weather:
  type: nominal
  handle_as: invalid_handle`,
			wantErr:     true,
			errContains: "unsupported handle_as",
		},
		{
			name: "incompatible type and handle_as",
			content: `weather:
  type: nominal
  handle_as: float`,
			wantErr:     true,
			errContains: "not compatible",
		},
		{
			name: "missing type field",
			content: `weather:
  handle_as: string`,
			wantErr:     true,
			errContains: "type field is required",
		},
		{
			name: "missing handle_as field",
			content: `weather:
  type: nominal`,
			wantErr:     true,
			errContains: "handle_as field is required",
		},
		{
			name: "empty feature name",
			content: `"":
  type: nominal
  handle_as: string`,
			wantErr:     true,
			errContains: "feature name cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test file
			testFile := filepath.Join(tempDir, tt.name+".yaml")
			if err := os.WriteFile(testFile, []byte(tt.content), 0644); err != nil {
				t.Fatalf("Failed to create test file: %v", err)
			}

			// Test LoadFeatureInfo
			config, err := loader.LoadFeatureInfo(testFile)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
				// Check that it's a FeatureLoaderError
				var loaderErr *FeatureLoaderError
				if !errors.As(err, &loaderErr) {
					t.Error("Expected FeatureLoaderError type")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if config == nil {
					t.Error("Expected non-nil config")
					return
				}
				if tt.validate != nil {
					tt.validate(t, config)
				}
			}
		})
	}
}

// TestLoadFeatureInfoNonExistentFile tests LoadFeatureInfo with a non-existent file.
func TestLoadFeatureInfoNonExistentFile(t *testing.T) {
	loader := NewFeatureLoader()

	_, err := loader.LoadFeatureInfo("/non/existent/file.yaml")

	if err == nil {
		t.Error("Expected error for non-existent file")
		return
	}

	if !strings.Contains(err.Error(), "cannot read file") {
		t.Errorf("Expected 'cannot read file' error, got: %v", err)
	}

	var loaderErr *FeatureLoaderError
	if !errors.As(err, &loaderErr) {
		t.Error("Expected FeatureLoaderError type")
	} else if loaderErr.Op != "read_file" {
		t.Errorf("Expected Op='read_file', got %q", loaderErr.Op)
	}
}

// TestValidateFeatureInfoAgainstCSV tests the CSV validation function.
func TestValidateFeatureInfoAgainstCSV(t *testing.T) {
	tests := []struct {
		name        string
		config      *YAMLFeatureConfig
		csvHeaders  []string
		wantErr     bool
		errContains string
	}{
		{
			name: "valid - all features exist in CSV",
			config: &YAMLFeatureConfig{
				"age": YAMLFeatureInfo{
					Type:     "numeric",
					HandleAs: "integer",
				},
				"name": YAMLFeatureInfo{
					Type:     "nominal",
					HandleAs: "string",
				},
			},
			csvHeaders: []string{"age", "name", "salary", "target"},
			wantErr:    false,
		},
		{
			name: "valid - partial feature definition",
			config: &YAMLFeatureConfig{
				"age": YAMLFeatureInfo{
					Type:     "numeric",
					HandleAs: "integer",
				},
			},
			csvHeaders: []string{"age", "name", "salary", "target"},
			wantErr:    false,
		},
		{
			name: "invalid - feature missing in CSV",
			config: &YAMLFeatureConfig{
				"age": YAMLFeatureInfo{
					Type:     "numeric",
					HandleAs: "integer",
				},
				"missing_feature": YAMLFeatureInfo{
					Type:     "nominal",
					HandleAs: "string",
				},
			},
			csvHeaders:  []string{"age", "name", "salary", "target"},
			wantErr:     true,
			errContains: "features defined in YAML but missing in CSV",
		},
		{
			name: "invalid - multiple features missing in CSV",
			config: &YAMLFeatureConfig{
				"missing1": YAMLFeatureInfo{
					Type:     "numeric",
					HandleAs: "integer",
				},
				"missing2": YAMLFeatureInfo{
					Type:     "nominal",
					HandleAs: "string",
				},
			},
			csvHeaders:  []string{"age", "name", "salary", "target"},
			wantErr:     true,
			errContains: "features defined in YAML but missing in CSV",
		},
		{
			name:        "invalid - nil config",
			config:      nil,
			csvHeaders:  []string{"age", "name"},
			wantErr:     true,
			errContains: "feature config is nil",
		},
		{
			name:       "valid - empty config",
			config:     &YAMLFeatureConfig{},
			csvHeaders: []string{"age", "name"},
			wantErr:    false,
		},
		{
			name:       "valid - empty CSV headers",
			config:     &YAMLFeatureConfig{},
			csvHeaders: []string{},
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateFeatureInfoAgainstCSV(tt.config, tt.csvHeaders, "test.yaml")

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
				// Check that it's a FeatureLoaderError
				var loaderErr *FeatureLoaderError
				if !errors.As(err, &loaderErr) {
					t.Error("Expected FeatureLoaderError type")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// TestValidateFeatureInfoAgainstCSVWithTarget tests the CSV validation function with target column.
func TestValidateFeatureInfoAgainstCSVWithTarget(t *testing.T) {
	tests := []struct {
		name         string
		config       *YAMLFeatureConfig
		csvHeaders   []string
		targetColumn string
		wantErr      bool
		errContains  string
	}{
		{
			name: "valid - target column in feature info",
			config: &YAMLFeatureConfig{
				"age": YAMLFeatureInfo{
					Type:     "numeric",
					HandleAs: "integer",
				},
				"target": YAMLFeatureInfo{
					Type:     "binary",
					HandleAs: "string",
				},
			},
			csvHeaders:   []string{"age", "name", "target"},
			targetColumn: "target",
			wantErr:      false,
		},
		{
			name: "valid - target column not in feature info",
			config: &YAMLFeatureConfig{
				"age": YAMLFeatureInfo{
					Type:     "numeric",
					HandleAs: "integer",
				},
			},
			csvHeaders:   []string{"age", "name", "target"},
			targetColumn: "target",
			wantErr:      false,
		},
		{
			name: "invalid - feature missing in CSV (target excluded)",
			config: &YAMLFeatureConfig{
				"missing_feature": YAMLFeatureInfo{
					Type:     "nominal",
					HandleAs: "string",
				},
			},
			csvHeaders:   []string{"age", "name", "target"},
			targetColumn: "target",
			wantErr:      true,
			errContains:  "features defined in YAML but missing in CSV",
		},
		{
			name: "valid - empty target column",
			config: &YAMLFeatureConfig{
				"age": YAMLFeatureInfo{
					Type:     "numeric",
					HandleAs: "integer",
				},
			},
			csvHeaders:   []string{"age", "name"},
			targetColumn: "",
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateFeatureInfoAgainstCSVWithTarget(tt.config, tt.csvHeaders, tt.targetColumn, "test.yaml")

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
				// Check that it's a FeatureLoaderError
				var loaderErr *FeatureLoaderError
				if !errors.As(err, &loaderErr) {
					t.Error("Expected FeatureLoaderError type")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}
