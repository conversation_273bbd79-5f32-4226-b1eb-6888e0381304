// Package features provides type conversion utilities for CSV data loading.
//
// Handles type conversion at loading into float, int or string as specified
// in project requirements. Converts raw CSV string data into the three core
// Go types based on YAML handle_as specifications.
//
// Security: Validates input data during conversion to prevent malformed data processing
// Performance: Efficient string parsing with early error detection
package features

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// TypeConverter handles conversion from CSV strings to Go types.
//
// Args: Conversion operations for different target types
// Security: Validates input data to prevent malformed data processing
// Performance: Stateless design enables concurrent usage
// Relationships: Used by Dataset loading process
// Side effects: Converts string data to typed values for internal storage
type TypeConverter struct{}

// NewTypeConverter creates a new type converter instance.
//
// Args: None
// Returns: converter ready for string-to-type conversion operations
// Security: Stateless design prevents state corruption
// Performance: No initialization overhead
func NewTypeConverter() *TypeConverter {
	return &TypeConverter{}
}

// ConvertValue converts raw CSV string to appropriate Go type based on handle_as.
//
// Args:
// - rawValue: String value from CSV file
// - handleAs: Target type from YAML (integer, float, string)
//
// Returns:
// - interface{}: converted value (int64, float64, or string)
// - error: conversion error with specific failure reason
//
// Security: Validates input before conversion
// Performance: Direct routing to type-specific converters
// Relationships: Main entry point for Dataset loading process
// Side effects: Converts string data to typed values
//
// Example: ConvertValue("25", "integer") → int64(25), nil
func (tc *TypeConverter) ConvertValue(rawValue string, handleAs string) (interface{}, error) {
	normalizedHandleAs := strings.ToLower(strings.TrimSpace(handleAs))

	switch normalizedHandleAs {
	case "integer":
		return tc.ConvertToInteger(rawValue)
	case "float":
		return tc.ConvertToFloat(rawValue)
	case "string":
		return tc.ConvertToString(rawValue)
	default:
		return nil, fmt.Errorf("unsupported handle_as type: %s", handleAs)
	}
}

// ConvertToInteger converts string to int64 with validation.
//
// Args:
// - value: String value to convert
//
// Returns:
// - int64: converted integer value
// - error: conversion error if value is not valid integer
//
// Security: Validates input format and range
// Performance: Uses strconv.ParseInt for efficient conversion
// Relationships: Used for age, experience_years, datetime timestamps
// Side effects: None (pure conversion function)
func (tc *TypeConverter) ConvertToInteger(value string) (int64, error) {
	trimmed := strings.TrimSpace(value)

	if trimmed == "" {
		return 0, fmt.Errorf("cannot convert empty string to integer")
	}

	result, err := strconv.ParseInt(trimmed, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid integer format '%s': %w", value, err)
	}

	return result, nil
}

// ConvertToFloat converts string to float64 with validation.
//
// Args:
// - value: String value to convert
//
// Returns:
// - float64: converted floating point value
// - error: conversion error if value is not valid float
//
// Security: Validates input format and handles special values (NaN, Inf)
// Performance: Uses strconv.ParseFloat for efficient conversion
// Relationships: Used for salary, temperature, gpa, height
// Side effects: None (pure conversion function)
func (tc *TypeConverter) ConvertToFloat(value string) (float64, error) {
	trimmed := strings.TrimSpace(value)

	if trimmed == "" {
		return 0.0, fmt.Errorf("cannot convert empty string to float")
	}

	result, err := strconv.ParseFloat(trimmed, 64)
	if err != nil {
		return 0.0, fmt.Errorf("invalid float format '%s': %w", value, err)
	}

	// Check for special float values that might cause issues
	if result != result { // NaN check
		return 0.0, fmt.Errorf("NaN values not supported: %s", value)
	}

	return result, nil
}

// ConvertToString validates and normalizes string values.
//
// Args:
// - value: String value to validate and normalize
//
// Returns:
// - string: validated and normalized string value
// - error: validation error (currently always nil, reserved for future validation)
//
// Security: Trims whitespace to prevent data inconsistencies
// Performance: Simple string operations
// Relationships: Used for weather, education, department, city, play_tennis
// Side effects: Normalizes whitespace in string values
func (tc *TypeConverter) ConvertToString(value string) (string, error) {
	// Trim whitespace for consistency
	normalized := strings.TrimSpace(value)

	// Allow empty strings - they will be handled as missing values by Dataset loading
	return normalized, nil
}

// ConvertDateTimeToInteger converts ISO8601 datetime string to Unix timestamp.
//
// Args:
// - dateTimeStr: ISO8601 formatted datetime string
//
// Returns:
// - int64: Unix timestamp (seconds since epoch)
// - error: parsing error if datetime format is invalid
//
// Security: Validates datetime format before conversion
// Performance: Uses time.Parse for standard datetime parsing
// Relationships: Used when YAML specifies type=datetime, handle_as=integer
// Side effects: None (pure conversion function)
//
// Example: ConvertDateTimeToInteger("2023-08-15T10:30:00Z") → 1692097800, nil
func (tc *TypeConverter) ConvertDateTimeToInteger(dateTimeStr string) (int64, error) {
	trimmed := strings.TrimSpace(dateTimeStr)

	if trimmed == "" {
		return 0, fmt.Errorf("cannot convert empty string to datetime")
	}

	// Try common ISO8601 formats
	formats := []string{
		time.RFC3339, // "2006-01-02T15:04:05Z07:00"
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05",
		"2006-01-02 15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, trimmed); err == nil {
			return t.Unix(), nil
		}
	}

	return 0, fmt.Errorf("invalid datetime format '%s', expected ISO8601", dateTimeStr)
}

// ConvertDateToInteger converts date string to Unix timestamp at midnight.
//
// Args:
// - dateStr: Date string in various formats
//
// Returns:
// - int64: Unix timestamp for date at 00:00:00 UTC
// - error: parsing error if date format is invalid
//
// Security: Validates date format before conversion
// Performance: Uses time.Parse for standard date parsing
// Relationships: Used when YAML specifies type=date, handle_as=integer
// Side effects: None (pure conversion function)
//
// Example: ConvertDateToInteger("2023-08-15") → 1692057600, nil
func (tc *TypeConverter) ConvertDateToInteger(dateStr string) (int64, error) {
	trimmed := strings.TrimSpace(dateStr)

	if trimmed == "" {
		return 0, fmt.Errorf("cannot convert empty string to date")
	}

	// Try common date formats
	formats := []string{
		"2006-01-02",
		"2006/01/02",
		"01/02/2006",
		"02-01-2006",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, trimmed); err == nil {
			// Return timestamp at midnight UTC
			return t.UTC().Truncate(24 * time.Hour).Unix(), nil
		}
	}

	return 0, fmt.Errorf("invalid date format '%s', expected YYYY-MM-DD or similar", dateStr)
}

// ConvertTimeToInteger converts time string to seconds since midnight.
//
// Args:
// - timeStr: Time string in HH:MM:SS or HH:MM format
//
// Returns:
// - int64: seconds since midnight (0-86399)
// - error: parsing error if time format is invalid
//
// Security: Validates time format and range
// Performance: Uses time.Parse for standard time parsing
// Relationships: Used when YAML specifies type=time, handle_as=integer
// Side effects: None (pure conversion function)
//
// Example: ConvertTimeToInteger("14:30:15") → 52215, nil
func (tc *TypeConverter) ConvertTimeToInteger(timeStr string) (int64, error) {
	trimmed := strings.TrimSpace(timeStr)

	if trimmed == "" {
		return 0, fmt.Errorf("cannot convert empty string to time")
	}

	// Try common time formats
	formats := []string{
		"15:04:05",   // HH:MM:SS
		"15:04",      // HH:MM
		"3:04:05 PM", // 12-hour format
		"3:04 PM",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, trimmed); err == nil {
			// Calculate seconds since midnight
			seconds := t.Hour()*3600 + t.Minute()*60 + t.Second()
			return int64(seconds), nil
		}
	}

	return 0, fmt.Errorf("invalid time format '%s', expected HH:MM:SS or HH:MM", timeStr)
}

// ConvertToFeatureType converts YAML handle_as to internal FeatureType.
//
// Args:
// - handleAs: Handle_as string from YAML (integer, float, string)
//
// Returns:
// - FeatureType: corresponding internal type
// - error: if handle_as value is invalid or unsupported
//
// Security: Validates handle_as against supported types
// Performance: Simple string comparison and mapping
// Relationships: Bridges YAML config to internal type system
// Side effects: Determines internal data processing approach
func ConvertToFeatureType(handleAs string) (FeatureType, error) {
	switch strings.ToLower(strings.TrimSpace(handleAs)) {
	case "integer":
		return IntegerFeature, nil
	case "float":
		return FloatFeature, nil
	case "string":
		return StringFeature, nil
	default:
		return StringFeature, fmt.Errorf("unsupported handle_as '%s'", handleAs)
	}
}
