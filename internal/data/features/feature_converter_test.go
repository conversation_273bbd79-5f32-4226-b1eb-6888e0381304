package features

import (
	"math"
	"strings"
	"testing"
)

// TestNewTypeConverter tests the constructor.
func TestNewTypeConverter(t *testing.T) {
	converter := NewTypeConverter()
	if converter == nil {
		t.Error("NewTypeConverter() returned nil")
	}
}

// TestConvertValue tests the main ConvertValue method.
func TestConvertValue(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		rawValue    string
		handleAs    string
		expected    interface{}
		wantErr     bool
		errContains string
	}{
		// Integer conversions
		{
			name:     "integer - positive",
			rawValue: "42",
			handleAs: "integer",
			expected: int64(42),
			wantErr:  false,
		},
		{
			name:     "integer - negative",
			rawValue: "-123",
			handleAs: "integer",
			expected: int64(-123),
			wantErr:  false,
		},
		{
			name:     "integer - zero",
			rawValue: "0",
			handleAs: "integer",
			expected: int64(0),
			wantErr:  false,
		},
		{
			name:     "integer - case insensitive",
			rawValue: "100",
			handleAs: "INTEGER",
			expected: int64(100),
			wantErr:  false,
		},
		{
			name:     "integer - with whitespace",
			rawValue: "  50  ",
			handleAs: "  integer  ",
			expected: int64(50),
			wantErr:  false,
		},

		// Float conversions
		{
			name:     "float - positive",
			rawValue: "3.14",
			handleAs: "float",
			expected: float64(3.14),
			wantErr:  false,
		},
		{
			name:     "float - negative",
			rawValue: "-2.5",
			handleAs: "float",
			expected: float64(-2.5),
			wantErr:  false,
		},
		{
			name:     "float - zero",
			rawValue: "0.0",
			handleAs: "float",
			expected: float64(0.0),
			wantErr:  false,
		},
		{
			name:     "float - integer format",
			rawValue: "42",
			handleAs: "float",
			expected: float64(42.0),
			wantErr:  false,
		},
		{
			name:     "float - case insensitive",
			rawValue: "1.5",
			handleAs: "FLOAT",
			expected: float64(1.5),
			wantErr:  false,
		},

		// String conversions
		{
			name:     "string - normal text",
			rawValue: "hello world",
			handleAs: "string",
			expected: "hello world",
			wantErr:  false,
		},
		{
			name:     "string - empty",
			rawValue: "",
			handleAs: "string",
			expected: "",
			wantErr:  false,
		},
		{
			name:     "string - with whitespace",
			rawValue: "  text  ",
			handleAs: "string",
			expected: "text",
			wantErr:  false,
		},
		{
			name:     "string - case insensitive",
			rawValue: "test",
			handleAs: "STRING",
			expected: "test",
			wantErr:  false,
		},

		// Error cases
		{
			name:        "unsupported handle_as",
			rawValue:    "value",
			handleAs:    "unsupported",
			wantErr:     true,
			errContains: "unsupported handle_as type",
		},
		{
			name:        "integer - invalid format",
			rawValue:    "not_a_number",
			handleAs:    "integer",
			wantErr:     true,
			errContains: "invalid integer format",
		},
		{
			name:        "float - invalid format",
			rawValue:    "not_a_float",
			handleAs:    "float",
			wantErr:     true,
			errContains: "invalid float format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertValue(tt.rawValue, tt.handleAs)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %v (%T), got %v (%T)", tt.expected, tt.expected, result, result)
				}
			}
		})
	}
}

// TestConvertToInteger tests integer conversion specifically.
func TestConvertToInteger(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		value       string
		expected    int64
		wantErr     bool
		errContains string
	}{
		{
			name:     "positive integer",
			value:    "123",
			expected: 123,
			wantErr:  false,
		},
		{
			name:     "negative integer",
			value:    "-456",
			expected: -456,
			wantErr:  false,
		},
		{
			name:     "zero",
			value:    "0",
			expected: 0,
			wantErr:  false,
		},
		{
			name:     "large positive",
			value:    "9223372036854775807", // max int64
			expected: 9223372036854775807,
			wantErr:  false,
		},
		{
			name:     "large negative",
			value:    "-9223372036854775808", // min int64
			expected: -9223372036854775808,
			wantErr:  false,
		},
		{
			name:     "with leading/trailing whitespace",
			value:    "  42  ",
			expected: 42,
			wantErr:  false,
		},
		{
			name:        "empty string",
			value:       "",
			wantErr:     true,
			errContains: "cannot convert empty string to integer",
		},
		{
			name:        "whitespace only",
			value:       "   ",
			wantErr:     true,
			errContains: "cannot convert empty string to integer",
		},
		{
			name:        "invalid format - text",
			value:       "abc",
			wantErr:     true,
			errContains: "invalid integer format",
		},
		{
			name:        "invalid format - float",
			value:       "3.14",
			wantErr:     true,
			errContains: "invalid integer format",
		},
		{
			name:        "invalid format - mixed",
			value:       "123abc",
			wantErr:     true,
			errContains: "invalid integer format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertToInteger(tt.value)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %d, got %d", tt.expected, result)
				}
			}
		})
	}
}

// TestConvertToFloat tests float conversion specifically.
func TestConvertToFloat(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		value       string
		expected    float64
		wantErr     bool
		errContains string
	}{
		{
			name:     "positive float",
			value:    "3.14",
			expected: 3.14,
			wantErr:  false,
		},
		{
			name:     "negative float",
			value:    "-2.5",
			expected: -2.5,
			wantErr:  false,
		},
		{
			name:     "zero",
			value:    "0.0",
			expected: 0.0,
			wantErr:  false,
		},
		{
			name:     "integer as float",
			value:    "42",
			expected: 42.0,
			wantErr:  false,
		},
		{
			name:     "scientific notation",
			value:    "1.23e-4",
			expected: 1.23e-4,
			wantErr:  false,
		},
		{
			name:     "with leading/trailing whitespace",
			value:    "  1.5  ",
			expected: 1.5,
			wantErr:  false,
		},
		{
			name:        "empty string",
			value:       "",
			wantErr:     true,
			errContains: "cannot convert empty string to float",
		},
		{
			name:        "whitespace only",
			value:       "   ",
			wantErr:     true,
			errContains: "cannot convert empty string to float",
		},
		{
			name:        "invalid format - text",
			value:       "abc",
			wantErr:     true,
			errContains: "invalid float format",
		},
		{
			name:        "invalid format - mixed",
			value:       "3.14abc",
			wantErr:     true,
			errContains: "invalid float format",
		},
		{
			name:        "NaN value",
			value:       "NaN",
			wantErr:     true,
			errContains: "NaN values not supported",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertToFloat(tt.value)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if math.Abs(result-tt.expected) > 1e-10 {
					t.Errorf("Expected %f, got %f", tt.expected, result)
				}
			}
		})
	}
}

// TestConvertToString tests string conversion specifically.
func TestConvertToString(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name     string
		value    string
		expected string
	}{
		{
			name:     "normal string",
			value:    "hello world",
			expected: "hello world",
		},
		{
			name:     "empty string",
			value:    "",
			expected: "",
		},
		{
			name:     "string with whitespace",
			value:    "  text with spaces  ",
			expected: "text with spaces",
		},
		{
			name:     "string with tabs and newlines",
			value:    "\t\ntext\t\n",
			expected: "text",
		},
		{
			name:     "numeric string",
			value:    "123",
			expected: "123",
		},
		{
			name:     "special characters",
			value:    "!@#$%^&*()",
			expected: "!@#$%^&*()",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertToString(tt.value)

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			if result != tt.expected {
				t.Errorf("Expected %q, got %q", tt.expected, result)
			}
		})
	}
}

// TestConvertDateTimeToInteger tests datetime to integer conversion.
func TestConvertDateTimeToInteger(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		dateTimeStr string
		expected    int64
		wantErr     bool
		errContains string
	}{
		{
			name:        "RFC3339 format",
			dateTimeStr: "2023-08-15T10:30:00Z",
			expected:    1692097800, // Unix timestamp for this date
			wantErr:     false,
		},
		{
			name:        "RFC3339 with timezone",
			dateTimeStr: "2023-08-15T10:30:00+02:00",
			expected:    1692090600, // Adjusted for timezone
			wantErr:     false,
		},
		{
			name:        "ISO8601 without timezone",
			dateTimeStr: "2023-08-15T10:30:00",
			expected:    1692097800,
			wantErr:     false,
		},
		{
			name:        "space separated format",
			dateTimeStr: "2023-08-15 10:30:00",
			expected:    1692097800,
			wantErr:     false,
		},
		{
			name:        "with whitespace",
			dateTimeStr: "  2023-08-15T10:30:00Z  ",
			expected:    1692097800,
			wantErr:     false,
		},
		{
			name:        "empty string",
			dateTimeStr: "",
			wantErr:     true,
			errContains: "cannot convert empty string to datetime",
		},
		{
			name:        "whitespace only",
			dateTimeStr: "   ",
			wantErr:     true,
			errContains: "cannot convert empty string to datetime",
		},
		{
			name:        "invalid format",
			dateTimeStr: "not-a-datetime",
			wantErr:     true,
			errContains: "invalid datetime format",
		},
		{
			name:        "partial date",
			dateTimeStr: "2023-08-15",
			wantErr:     true,
			errContains: "invalid datetime format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertDateTimeToInteger(tt.dateTimeStr)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				// For datetime tests, check that result is a reasonable timestamp (within a day of expected)
				// This accounts for timezone differences in test environments
				diff := result - tt.expected
				if diff < 0 {
					diff = -diff
				}
				if diff > 86400 { // 24 hours in seconds
					t.Errorf("Expected timestamp around %d, got %d (diff: %d)", tt.expected, result, diff)
				}
			}
		})
	}
}

// TestConvertDateToInteger tests date to integer conversion.
func TestConvertDateToInteger(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		dateStr     string
		expected    int64
		wantErr     bool
		errContains string
	}{
		{
			name:     "ISO date format",
			dateStr:  "2023-08-15",
			expected: **********, // Unix timestamp for 2023-08-15 00:00:00 UTC
			wantErr:  false,
		},
		{
			name:     "US date format",
			dateStr:  "08/15/2023",
			expected: **********,
			wantErr:  false,
		},
		{
			name:        "European date format (unsupported)",
			dateStr:     "15/08/2023",
			wantErr:     true,
			errContains: "invalid date format",
		},
		{
			name:     "dash separated European",
			dateStr:  "15-08-2023",
			expected: **********,
			wantErr:  false,
		},
		{
			name:     "with whitespace",
			dateStr:  "  2023-08-15  ",
			expected: **********,
			wantErr:  false,
		},
		{
			name:        "empty string",
			dateStr:     "",
			wantErr:     true,
			errContains: "cannot convert empty string to date",
		},
		{
			name:        "whitespace only",
			dateStr:     "   ",
			wantErr:     true,
			errContains: "cannot convert empty string to date",
		},
		{
			name:        "invalid format",
			dateStr:     "not-a-date",
			wantErr:     true,
			errContains: "invalid date format",
		},
		{
			name:        "datetime instead of date",
			dateStr:     "2023-08-15T10:30:00Z",
			wantErr:     true,
			errContains: "invalid date format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertDateToInteger(tt.dateStr)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %d, got %d", tt.expected, result)
				}
			}
		})
	}
}

// TestConvertTimeToInteger tests time to integer conversion.
func TestConvertTimeToInteger(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		timeStr     string
		expected    int64
		wantErr     bool
		errContains string
	}{
		{
			name:     "24-hour format with seconds",
			timeStr:  "14:30:15",
			expected: 52215, // 14*3600 + 30*60 + 15
			wantErr:  false,
		},
		{
			name:     "24-hour format without seconds",
			timeStr:  "14:30",
			expected: 52200, // 14*3600 + 30*60
			wantErr:  false,
		},
		{
			name:     "12-hour format with seconds",
			timeStr:  "2:30:15 PM",
			expected: 52215, // Same as 14:30:15
			wantErr:  false,
		},
		{
			name:     "12-hour format without seconds",
			timeStr:  "2:30 PM",
			expected: 52200, // Same as 14:30
			wantErr:  false,
		},
		{
			name:     "midnight",
			timeStr:  "00:00:00",
			expected: 0,
			wantErr:  false,
		},
		{
			name:     "end of day",
			timeStr:  "23:59:59",
			expected: 86399, // 23*3600 + 59*60 + 59
			wantErr:  false,
		},
		{
			name:     "with whitespace",
			timeStr:  "  14:30:15  ",
			expected: 52215,
			wantErr:  false,
		},
		{
			name:        "empty string",
			timeStr:     "",
			wantErr:     true,
			errContains: "cannot convert empty string to time",
		},
		{
			name:        "whitespace only",
			timeStr:     "   ",
			wantErr:     true,
			errContains: "cannot convert empty string to time",
		},
		{
			name:        "invalid format",
			timeStr:     "not-a-time",
			wantErr:     true,
			errContains: "invalid time format",
		},
		{
			name:        "invalid hour",
			timeStr:     "25:30:00",
			wantErr:     true,
			errContains: "invalid time format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertTimeToInteger(tt.timeStr)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %d, got %d", tt.expected, result)
				}
			}
		})
	}
}

// TestConvertToFeatureType tests the ConvertToFeatureType function.
func TestConvertToFeatureType(t *testing.T) {
	tests := []struct {
		name        string
		handleAs    string
		expected    FeatureType
		wantErr     bool
		errContains string
	}{
		{
			name:     "integer type",
			handleAs: "integer",
			expected: IntegerFeature,
			wantErr:  false,
		},
		{
			name:     "float type",
			handleAs: "float",
			expected: FloatFeature,
			wantErr:  false,
		},
		{
			name:     "string type",
			handleAs: "string",
			expected: StringFeature,
			wantErr:  false,
		},
		{
			name:     "case insensitive - INTEGER",
			handleAs: "INTEGER",
			expected: IntegerFeature,
			wantErr:  false,
		},
		{
			name:     "case insensitive - FLOAT",
			handleAs: "FLOAT",
			expected: FloatFeature,
			wantErr:  false,
		},
		{
			name:     "case insensitive - STRING",
			handleAs: "STRING",
			expected: StringFeature,
			wantErr:  false,
		},
		{
			name:     "mixed case - Integer",
			handleAs: "Integer",
			expected: IntegerFeature,
			wantErr:  false,
		},
		{
			name:     "with whitespace",
			handleAs: "  string  ",
			expected: StringFeature,
			wantErr:  false,
		},
		{
			name:        "unsupported type",
			handleAs:    "unsupported",
			expected:    StringFeature, // Default fallback
			wantErr:     true,
			errContains: "unsupported handle_as",
		},
		{
			name:        "empty string",
			handleAs:    "",
			expected:    StringFeature, // Default fallback
			wantErr:     true,
			errContains: "unsupported handle_as",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ConvertToFeatureType(tt.handleAs)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
			}

			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

// BenchmarkConvertValue benchmarks the main ConvertValue method.
func BenchmarkConvertValue(b *testing.B) {
	converter := NewTypeConverter()

	testCases := []struct {
		rawValue string
		handleAs string
	}{
		{"42", "integer"},
		{"3.14", "float"},
		{"hello", "string"},
		{"123", "integer"},
		{"2.5", "float"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		testCase := testCases[i%len(testCases)]
		_, _ = converter.ConvertValue(testCase.rawValue, testCase.handleAs)
	}
}
