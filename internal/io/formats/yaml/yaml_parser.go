// Package yaml provides YAML content parsing and structure validation utilities.
//
// Handles YAML format operations including content parsing and structure validation.
// Used by feature metadata loading and other configuration file processing throughout Mulberri.
// Pre-file validation (existence, security, size limits) is handled by CLI validation helpers.
//
// Security: Validates content structure (file path validation done by CLI layer)
// Performance: Efficient YAML operations with minimal memory allocations
package yaml

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

// Note: File size limits and pre-file validation have been moved to CLI validation helpers
// This package now focuses on parsing and content validation only

// ValidationError represents errors during YAML operations.
//
// Args: Structured error context for debugging and user feedback
// Security: May contain file paths (ensure no sensitive path exposure)
// Relationships: Used throughout YAML processing pipeline
type ValidationError struct {
	Op   string // Operation that failed
	File string // File path being processed
	Err  error  // Underlying error
}

// Error implements the error interface with structured context.
//
// Args: None (method receiver)
// Returns: formatted error message with operation context
func (e *ValidationError) Error() string {
	return fmt.Sprintf("YAML %s error in file '%s': %v", e.Op, e.File, e.Err)
}

// Unwrap returns the underlying error for error chain inspection.
//
// Args: None (method receiver)
// Returns: underlying error for error wrapping support
func (e *ValidationError) Unwrap() error {
	return e.Err
}

// ParseFile parses a YAML file into the provided interface.
//
// Args:
// - filePath: Path to YAML file (must be pre-validated by CLI validation helpers)
// - target: Pointer to struct to unmarshal into
//
// Returns: error if parsing fails, nil if successful
// Security: Assumes filePath already validated by CLI validation helpers (validateYAMLFile)
// Performance: Efficient unmarshaling using yaml.v3
// Side effects: Reads file from disk and modifies target struct
//
// Example:
//
//	var config MyConfig
//	err := yaml.ParseFile("config.yaml", &config)
func ParseFile(filePath string, target interface{}) error {
	// Read file content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return &ValidationError{
			Op:   "read_file",
			File: filePath,
			Err:  fmt.Errorf("cannot read file: %w", err),
		}
	}

	// Check for empty content
	if len(strings.TrimSpace(string(data))) == 0 {
		return &ValidationError{
			Op:   "validate_content",
			File: filePath,
			Err:  fmt.Errorf("file is empty or contains only whitespace"),
		}
	}

	// Parse YAML content
	if err := yaml.Unmarshal(data, target); err != nil {
		return &ValidationError{
			Op:   "parse_yaml",
			File: filePath,
			Err:  fmt.Errorf("invalid YAML format: %w", err),
		}
	}

	return nil
}

// ValidateStructure performs basic structure validation on parsed YAML.
//
// Args:
// - data: Parsed YAML data as interface{}
// - filePath: File path for error context
//
// Returns: error if structure is invalid, nil if valid
// Security: Prevents processing of malformed data structures
// Performance: Quick validation before expensive processing
func ValidateStructure(data interface{}, filePath string) error {
	if data == nil {
		return &ValidationError{
			Op:   "validate_structure",
			File: filePath,
			Err:  fmt.Errorf("parsed data is nil"),
		}
	}

	// Ensure data is a map (common requirement for configuration files)
	switch v := data.(type) {
	case map[string]interface{}:
		if len(v) == 0 {
			return &ValidationError{
				Op:   "validate_structure",
				File: filePath,
				Err:  fmt.Errorf("configuration is empty"),
			}
		}
	case map[interface{}]interface{}:
		if len(v) == 0 {
			return &ValidationError{
				Op:   "validate_structure",
				File: filePath,
				Err:  fmt.Errorf("configuration is empty"),
			}
		}
	default:
		return &ValidationError{
			Op:   "validate_structure",
			File: filePath,
			Err:  fmt.Errorf("expected configuration map, got %T", data),
		}
	}

	return nil
}
