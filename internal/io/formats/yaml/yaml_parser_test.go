package yaml

import (
	"errors"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// TestValidationError tests the ValidationError type and its methods.
func TestValidationError(t *testing.T) {
	tests := []struct {
		name     string
		err      *ValidationError
		expected string
	}{
		{
			name: "basic error",
			err: &ValidationError{
				Op:   "parse_yaml",
				File: "test.yaml",
				Err:  errors.New("syntax error"),
			},
			expected: "YAML parse_yaml error in file 'test.yaml': syntax error",
		},
		{
			name: "read file error",
			err: &ValidationError{
				Op:   "read_file",
				File: "/path/to/file.yaml",
				Err:  errors.New("file not found"),
			},
			expected: "YAML read_file error in file '/path/to/file.yaml': file not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.err.Error(); got != tt.expected {
				t.Errorf("ValidationError.Error() = %q, want %q", got, tt.expected)
			}
		})
	}
}

// TestValidationErrorUnwrap tests the Unwrap method.
func TestValidationErrorUnwrap(t *testing.T) {
	originalErr := errors.New("original error")
	validationErr := &ValidationError{
		Op:   "test",
		File: "test.yaml",
		Err:  originalErr,
	}

	if unwrapped := validationErr.Unwrap(); unwrapped != originalErr {
		t.Errorf("ValidationError.Unwrap() = %v, want %v", unwrapped, originalErr)
	}
}

// TestParseFile tests the ParseFile function with various scenarios.
func TestParseFile(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := os.MkdirTemp("", "yaml_parser_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name        string
		content     string
		target      interface{}
		wantErr     bool
		errContains string
		validate    func(t *testing.T, target interface{})
	}{
		{
			name:    "valid YAML - simple map",
			content: "key1: value1\nkey2: value2\n",
			target:  &map[string]string{},
			wantErr: false,
			validate: func(t *testing.T, target interface{}) {
				result := target.(*map[string]string)
				expected := map[string]string{"key1": "value1", "key2": "value2"}
				if len(*result) != len(expected) {
					t.Errorf("Expected %d items, got %d", len(expected), len(*result))
				}
				for k, v := range expected {
					if (*result)[k] != v {
						t.Errorf("Expected %s=%s, got %s", k, v, (*result)[k])
					}
				}
			},
		},
		{
			name:    "valid YAML - nested structure",
			content: "database:\n  host: localhost\n  port: 5432\n",
			target:  &map[string]interface{}{},
			wantErr: false,
			validate: func(t *testing.T, target interface{}) {
				result := target.(*map[string]interface{})
				if db, ok := (*result)["database"]; ok {
					if dbMap, ok := db.(map[string]interface{}); ok {
						if dbMap["host"] != "localhost" {
							t.Errorf("Expected host=localhost, got %v", dbMap["host"])
						}
						if dbMap["port"] != 5432 {
							t.Errorf("Expected port=5432, got %v", dbMap["port"])
						}
					} else {
						t.Error("Database should be a map")
					}
				} else {
					t.Error("Database key not found")
				}
			},
		},
		{
			name:        "empty file",
			content:     "",
			target:      &map[string]string{},
			wantErr:     true,
			errContains: "empty or contains only whitespace",
		},
		{
			name:        "whitespace only file",
			content:     "   \n\t  \n  ",
			target:      &map[string]string{},
			wantErr:     true,
			errContains: "empty or contains only whitespace",
		},
		{
			name:        "invalid YAML syntax",
			content:     "key1: value1\n  invalid: [unclosed",
			target:      &map[string]string{},
			wantErr:     true,
			errContains: "invalid YAML format",
		},
		{
			name:    "YAML with tabs (should work)",
			content: "key1:\tvalue1\nkey2:\tvalue2",
			target:  &map[string]string{},
			wantErr: false,
			validate: func(t *testing.T, target interface{}) {
				result := target.(*map[string]string)
				if (*result)["key1"] != "value1" || (*result)["key2"] != "value2" {
					t.Errorf("Tab-separated YAML not parsed correctly: %+v", *result)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test file
			testFile := filepath.Join(tempDir, tt.name+".yaml")
			if err := os.WriteFile(testFile, []byte(tt.content), 0644); err != nil {
				t.Fatalf("Failed to create test file: %v", err)
			}

			// Test ParseFile
			err := ParseFile(testFile, tt.target)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
				// Check that it's a ValidationError
				var validationErr *ValidationError
				if !errors.As(err, &validationErr) {
					t.Error("Expected ValidationError type")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if tt.validate != nil {
					tt.validate(t, tt.target)
				}
			}
		})
	}
}

// TestParseFileNonExistentFile tests ParseFile with a non-existent file.
func TestParseFileNonExistentFile(t *testing.T) {
	var target map[string]string
	err := ParseFile("/non/existent/file.yaml", &target)

	if err == nil {
		t.Error("Expected error for non-existent file")
		return
	}

	if !strings.Contains(err.Error(), "cannot read file") {
		t.Errorf("Expected 'cannot read file' error, got: %v", err)
	}

	var validationErr *ValidationError
	if !errors.As(err, &validationErr) {
		t.Error("Expected ValidationError type")
	} else if validationErr.Op != "read_file" {
		t.Errorf("Expected Op='read_file', got %q", validationErr.Op)
	}
}

// TestValidateStructure tests the ValidateStructure function.
func TestValidateStructure(t *testing.T) {
	tests := []struct {
		name        string
		data        interface{}
		filePath    string
		wantErr     bool
		errContains string
	}{
		{
			name:     "valid map[string]interface{}",
			data:     map[string]interface{}{"key": "value"},
			filePath: "test.yaml",
			wantErr:  false,
		},
		{
			name:     "valid map[interface{}]interface{}",
			data:     map[interface{}]interface{}{"key": "value"},
			filePath: "test.yaml",
			wantErr:  false,
		},
		{
			name:        "nil data",
			data:        nil,
			filePath:    "test.yaml",
			wantErr:     true,
			errContains: "parsed data is nil",
		},
		{
			name:        "empty map[string]interface{}",
			data:        map[string]interface{}{},
			filePath:    "test.yaml",
			wantErr:     true,
			errContains: "configuration is empty",
		},
		{
			name:        "empty map[interface{}]interface{}",
			data:        map[interface{}]interface{}{},
			filePath:    "test.yaml",
			wantErr:     true,
			errContains: "configuration is empty",
		},
		{
			name:        "invalid type - string",
			data:        "not a map",
			filePath:    "test.yaml",
			wantErr:     true,
			errContains: "expected configuration map, got string",
		},
		{
			name:        "invalid type - slice",
			data:        []string{"item1", "item2"},
			filePath:    "test.yaml",
			wantErr:     true,
			errContains: "expected configuration map, got []string",
		},
		{
			name:        "invalid type - int",
			data:        42,
			filePath:    "test.yaml",
			wantErr:     true,
			errContains: "expected configuration map, got int",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateStructure(tt.data, tt.filePath)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Expected error containing %q, got %q", tt.errContains, err.Error())
				}
				// Check that it's a ValidationError
				var validationErr *ValidationError
				if !errors.As(err, &validationErr) {
					t.Error("Expected ValidationError type")
				} else {
					if validationErr.Op != "validate_structure" {
						t.Errorf("Expected Op='validate_structure', got %q", validationErr.Op)
					}
					if validationErr.File != tt.filePath {
						t.Errorf("Expected File=%q, got %q", tt.filePath, validationErr.File)
					}
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// BenchmarkParseFile benchmarks the ParseFile function.
func BenchmarkParseFile(b *testing.B) {
	// Create temporary file for benchmarking
	tempDir, err := os.MkdirTemp("", "yaml_parser_bench")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	content := `
database:
  host: localhost
  port: 5432
  name: testdb
features:
  feature1:
    type: numeric
    handle_as: float
  feature2:
    type: nominal
    handle_as: string
  feature3:
    type: binary
    handle_as: integer
`
	testFile := filepath.Join(tempDir, "benchmark.yaml")
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		b.Fatalf("Failed to create test file: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var target map[string]interface{}
		_ = ParseFile(testFile, &target)
	}
}

// BenchmarkValidateStructure benchmarks the ValidateStructure function.
func BenchmarkValidateStructure(b *testing.B) {
	data := map[string]interface{}{
		"key1": "value1",
		"key2": map[string]interface{}{
			"nested1": "value2",
			"nested2": 42,
		},
		"key3": []string{"item1", "item2"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = ValidateStructure(data, "test.yaml")
	}
}
