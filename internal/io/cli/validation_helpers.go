package cli

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// validateFile performs comprehensive file validation including security, existence, and properties.
//
// Args:
// - filePath: Path to the file to validate
// - fileType: Description of the file type for error messages (e.g., "input file", "model file")
// - maxSize: Maximum allowed file size in bytes (0 means no limit)
//
// Returns: error if any validation fails, nil if all validations pass
// Security: Includes path traversal protection and permission verification
// Performance: Early validation prevents expensive processing of invalid files
// Side effects: Checks file system for existence, permissions, and attempts file open
func validateFile(filePath, fileType string) error {
	// Basic path validation
	cleanPath := strings.TrimSpace(filePath)
	if cleanPath == "" {
		return fmt.Errorf("%s path is required", fileType)
	}

	// Clean and normalize path
	cleanPath = filepath.Clean(cleanPath)

	// Check file existence and get info
	_, err := os.Stat(cleanPath)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", cleanPath)
	}
	if err != nil {
		return fmt.Errorf("cannot access file: %w", err)
	}
	// Verify read permissions by attempting to open
	file, err := os.Open(cleanPath)
	if err != nil {
		return fmt.Errorf("cannot read file: %w", err)
	}
	defer file.Close()

	return nil
}

// validateFileExtension validates that a file has one of the allowed extensions.
//
// Args:
// - filePath: Path to the file to validate
// - allowedExts: Slice of allowed extensions (e.g., []string{".csv", ".txt"})
// - fileType: Description of the file type for error messages
//
// Returns: error if extension is not in allowed list, nil if valid
func validateFileExtension(filePath string, allowedExts []string, fileType string) error {
	ext := strings.ToLower(filepath.Ext(filePath))

	for _, allowedExt := range allowedExts {
		if ext == strings.ToLower(allowedExt) {
			return nil
		}
	}

	if len(allowedExts) == 1 {
		return fmt.Errorf("%s must have %s extension, got: %s", fileType, allowedExts[0], ext)
	}

	return fmt.Errorf("%s must be in format %v, got: %s", fileType, allowedExts, ext)
}

// validateInputFile validates CSV input file existence and format.
//
// Args:
// - inputFile: Path to input CSV file
//
// Returns error if file doesn't exist, has wrong extension, or is directory.
// Security: Includes comprehensive validation with size limits and security checks
func validateInputFile(inputFile string) error {
	// Comprehensive validation with size limit
	if err := validateFile(inputFile, "input file"); err != nil {
		return err
	}

	// Validate file extension
	return validateFileExtension(inputFile, []string{".csv"}, "input file")
}

// validateModelFile validates trained model file existence and format.
//
// Args:
// - modelFile: Path to trained model file
//
// Returns error if file doesn't exist or has wrong extension (.dt required).
// Security: Includes comprehensive validation with size limits and security checks
func validateModelFile(modelFile string) error {
	// Comprehensive validation with size limit
	if err := validateFile(modelFile, "model file"); err != nil {
		return err
	}

	// Validate file extension
	return validateFileExtension(modelFile, []string{".dt"}, "model file")
}

// validateYAMLFile validates YAML file existence and format.
//
// Args:
// - yamlFile: Path to YAML file
//
// Returns error if file doesn't exist or has wrong extension (.yaml/.yml required).
// Security: Includes comprehensive validation with size limits and security checks
func validateYAMLFile(yamlFile string) error {
	// Comprehensive validation with size limit
	if err := validateFile(yamlFile, "YAML file"); err != nil {
		return err
	}

	// Validate file extension
	return validateFileExtension(yamlFile, []string{".yaml", ".yml"}, "YAML file")
}
