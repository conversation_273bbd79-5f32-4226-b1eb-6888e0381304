package cli

import (
	"bytes"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestNewPredictCommand(t *testing.T) {
	predictCmd := NewPredictCommand()

	// Test basic command properties
	if predictCmd.Use != "predict" {
		t.<PERSON>rrorf("expected Use='predict', got=%s", predictCmd.Use)
	}

	if predictCmd.Short == "" {
		t.<PERSON>("expected non-empty Short description")
	}

	if predictCmd.Long == "" {
		t.<PERSON>r("expected non-empty Long description")
	}

	if predictCmd.Example == "" {
		t.Error("expected non-empty Example")
	}

	if predictCmd.RunE == nil {
		t.Error("expected RunE function to be set")
	}

	// Test that required flags exist
	requiredFlags := []string{"input", "model", "output"}
	for _, flagName := range requiredFlags {
		flag := predictCmd.Flags().Lookup(flagName)
		if flag == nil {
			t.Errorf("required flag %s not found", flagName)
		}
	}

	// Test optional flags exist
	verboseFlag := predictCmd.Flags().Lookup("verbose")
	if verboseFlag == nil {
		t.E<PERSON>r("verbose flag not found")
	}
}

func TestPredictCommandHelp(t *testing.T) {
	predictCmd := NewPredictCommand()

	// Capture help output
	var buf bytes.Buffer
	predictCmd.SetOut(&buf)
	predictCmd.SetArgs([]string{"--help"})

	err := predictCmd.Execute()
	if err != nil {
		t.Errorf("help command should not return error: %v", err)
	}

	helpOutput := buf.String()
	expectedStrings := []string{
		"predict",
		"predictions",
		"--input",
		"--model",
		"--output",
		"--verbose",
		"Examples:",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(helpOutput, expected) {
			t.Errorf("help output missing expected string: %s", expected)
		}
	}
}

func TestPredictCommandValidation(t *testing.T) {
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validModel := filepath.Join(tempDir, "test.dt")

	// Create test files
	if err := os.WriteFile(validCSV, []byte("col1,col2\n1,2\n3,4\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test model: %v", err)
	}

	tests := []struct {
		name    string
		args    []string
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid prediction command",
			args: []string{
				"--input", validCSV,
				"--model", validModel,
				"--output", "predictions.csv",
			},
			wantErr: false,
		},
		{
			name: "valid with verbose flag",
			args: []string{
				"--input", validCSV,
				"--model", validModel,
				"--output", "predictions.csv",
				"--verbose",
			},
			wantErr: false,
		},
		{
			name: "valid with short flags",
			args: []string{
				"-i", validCSV,
				"-m", validModel,
				"-o", "predictions.csv",
				"-v",
			},
			wantErr: false,
		},
		{
			name: "missing required input flag",
			args: []string{
				"--model", validModel,
				"--output", "predictions.csv",
			},
			wantErr: true,
			errMsg:  "required flag(s)",
		},
		{
			name: "missing required model flag",
			args: []string{
				"--input", validCSV,
				"--output", "predictions.csv",
			},
			wantErr: true,
			errMsg:  "required flag(s)",
		},
		{
			name: "missing required output flag",
			args: []string{
				"--input", validCSV,
				"--model", validModel,
			},
			wantErr: true,
			errMsg:  "required flag(s)",
		},
		{
			name: "nonexistent input file",
			args: []string{
				"--input", "nonexistent.csv",
				"--model", validModel,
				"--output", "predictions.csv",
			},
			wantErr: true,
			errMsg:  "file does not exist",
		},
		{
			name: "nonexistent model file",
			args: []string{
				"--input", validCSV,
				"--model", "nonexistent.dt",
				"--output", "predictions.csv",
			},
			wantErr: true,
			errMsg:  "file does not exist",
		},
		{
			name: "invalid input file extension",
			args: []string{
				"--input", validModel, // Using .dt instead of .csv
				"--model", validModel,
				"--output", "predictions.csv",
			},
			wantErr: true,
			errMsg:  "input file must have .csv extension",
		},
		{
			name: "invalid model file extension",
			args: []string{
				"--input", validCSV,
				"--model", validCSV, // Using .csv instead of .dt
				"--output", "predictions.csv",
			},
			wantErr: true,
			errMsg:  "model file must have .dt extension",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			predictCmd := NewPredictCommand()

			// Capture output for validation
			var buf bytes.Buffer
			predictCmd.SetOut(&buf)
			predictCmd.SetErr(&buf)
			predictCmd.SetArgs(tt.args)

			err := predictCmd.Execute()

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestRunPrediction(t *testing.T) {
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validModel := filepath.Join(tempDir, "test.dt")

	// Create test files
	if err := os.WriteFile(validCSV, []byte("col1,col2\n1,2\n3,4\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test model: %v", err)
	}

	tests := []struct {
		name    string
		config  *PredictionConfig
		wantErr bool
	}{
		{
			name: "valid prediction config",
			config: &PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  validModel,
				OutputFile: "predictions.csv",
				Verbose:    false,
			},
			wantErr: false,
		},
		{
			name: "valid prediction config with verbose",
			config: &PredictionConfig{
				InputFile:  validCSV,
				ModelFile:  validModel,
				OutputFile: "predictions.csv",
				Verbose:    true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := runPrediction(tt.config)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// Test flag aliases work correctly
func TestPredictCommandFlagAliases(t *testing.T) {
	tempDir := t.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validModel := filepath.Join(tempDir, "test.dt")

	// Create test files
	if err := os.WriteFile(validCSV, []byte("col1,col2\n1,2\n"), 0644); err != nil {
		t.Fatalf("failed to create test CSV: %v", err)
	}
	if err := os.WriteFile(validModel, []byte("{}"), 0644); err != nil {
		t.Fatalf("failed to create test model: %v", err)
	}

	flagTests := []struct {
		longFlag  string
		shortFlag string
		value     string
	}{
		{"--input", "-i", validCSV},
		{"--model", "-m", validModel},
		{"--output", "-o", "predictions.csv"},
		{"--verbose", "-v", ""},
	}

	for _, tt := range flagTests {
		t.Run("test_"+tt.longFlag+"_alias", func(t *testing.T) {
			// Test long flag
			longCmd := NewPredictCommand()
			longArgs := []string{
				"--input", validCSV,
				"--model", validModel,
				"--output", "predictions.csv",
			}
			longCmd.SetArgs(longArgs)
			err := longCmd.ParseFlags(longArgs)
			if err != nil {
				t.Errorf("long flag parsing failed: %v", err)
			}

			// Test short flag
			shortCmd := NewPredictCommand()
			shortArgs := []string{
				"-i", validCSV,
				"-m", validModel,
				"-o", "predictions.csv",
			}
			shortCmd.SetArgs(shortArgs)
			err = shortCmd.ParseFlags(shortArgs)
			if err != nil {
				t.Errorf("short flag parsing failed: %v", err)
			}
		})
	}
}

// Benchmark predict command creation and execution
func BenchmarkNewPredictCommand(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewPredictCommand()
	}
}

func BenchmarkRunPrediction(b *testing.B) {
	tempDir := b.TempDir()
	validCSV := filepath.Join(tempDir, "test.csv")
	validModel := filepath.Join(tempDir, "test.dt")

	os.WriteFile(validCSV, []byte("col1,col2\n1,2\n"), 0644)
	os.WriteFile(validModel, []byte("{}"), 0644)

	cfg := &PredictionConfig{
		InputFile:  validCSV,
		ModelFile:  validModel,
		OutputFile: "predictions.csv",
		Verbose:    false,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = runPrediction(cfg)
	}
}
